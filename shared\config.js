// Shared configuration for all microservices
module.exports = {
  // Service ports
  services: {
    gateway: {
      port: process.env.GATEWAY_PORT || 3000,
      name: 'api-gateway'
    },
    user: {
      port: process.env.USER_SERVICE_PORT || 3001,
      name: 'user-service',
      url: process.env.USER_SERVICE_URL || 'http://localhost:3001'
    },
    product: {
      port: process.env.PRODUCT_SERVICE_PORT || 3002,
      name: 'product-service',
      url: process.env.PRODUCT_SERVICE_URL || 'http://localhost:3002'
    }
  },

  // JWT configuration
  jwt: {
    secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production',
    expiresIn: process.env.JWT_EXPIRES_IN || '24h'
  },

  // Database configuration (for future use)
  database: {
    url: process.env.DATABASE_URL || 'mongodb://localhost:27017/microservices',
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true
    }
  },

  // CORS configuration
  cors: {
    origin: process.env.CORS_ORIGIN || '*',
    credentials: true
  },

  // Rate limiting
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100 // limit each IP to 100 requests per windowMs
  },

  // Logging configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    format: process.env.LOG_FORMAT || 'combined'
  }
};
