{"name": "user-service", "version": "1.0.0", "description": "User management microservice", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "helmet": "^7.0.0", "cors": "^2.8.5", "morgan": "^1.10.0", "express-rate-limit": "^6.10.0", "dotenv": "^16.3.1", "joi": "^17.9.2"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3"}, "keywords": ["microservice", "user", "authentication"], "author": "Your Name", "license": "MIT"}