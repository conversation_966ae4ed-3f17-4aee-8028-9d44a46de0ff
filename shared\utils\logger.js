const config = require('../config');

// Simple logger utility
class Logger {
  constructor(serviceName) {
    this.serviceName = serviceName;
  }

  formatMessage(level, message, meta = {}) {
    return {
      timestamp: new Date().toISOString(),
      service: this.serviceName,
      level: level.toUpperCase(),
      message,
      ...meta
    };
  }

  info(message, meta = {}) {
    console.log(JSON.stringify(this.formatMessage('info', message, meta)));
  }

  error(message, meta = {}) {
    console.error(JSON.stringify(this.formatMessage('error', message, meta)));
  }

  warn(message, meta = {}) {
    console.warn(JSON.stringify(this.formatMessage('warn', message, meta)));
  }

  debug(message, meta = {}) {
    if (config.logging.level === 'debug') {
      console.log(JSON.stringify(this.formatMessage('debug', message, meta)));
    }
  }
}

module.exports = Logger;
