# Learn Microservices with Node.js

This project demonstrates how to build microservices using Node.js, Express, and various supporting technologies.

## Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Gateway   │    │  User Service   │    │ Product Service │
│   Port: 3000    │    │   Port: 3001    │    │   Port: 3002    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Shared Utils  │
                    │   & Config      │
                    └─────────────────┘
```

## Services

### 1. API Gateway (Port 3000)
- Entry point for all client requests
- Routes requests to appropriate microservices
- Handles authentication and rate limiting
- Load balancing and service discovery

### 2. User Service (Port 3001)
- User registration and authentication
- User profile management
- JWT token generation and validation

### 3. Product Service (Port 3002)
- Product catalog management
- Inventory tracking
- Product search and filtering

## Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn

### Installation

1. Clone the repository
2. Install dependencies for all services:
   ```bash
   npm run install:all
   ```

### Running the Services

#### Option 1: Start all services at once
```bash
npm run dev
```

#### Option 2: Start services individually
```bash
# Terminal 1 - API Gateway
npm run start:gateway

# Terminal 2 - User Service
npm run start:user

# Terminal 3 - Product Service
npm run start:product
```

## API Endpoints

### User Service (via Gateway: http://localhost:3000/api/users)
- `POST /api/users/register` - Register a new user
- `POST /api/users/login` - User login
- `GET /api/users/profile` - Get user profile (requires auth)
- `PUT /api/users/profile` - Update user profile (requires auth)

### Product Service (via Gateway: http://localhost:3000/api/products)
- `GET /api/products` - Get all products
- `GET /api/products/:id` - Get product by ID
- `POST /api/products` - Create new product (requires auth)
- `PUT /api/products/:id` - Update product (requires auth)
- `DELETE /api/products/:id` - Delete product (requires auth)

## Key Concepts Demonstrated

1. **Service Independence**: Each service runs independently
2. **API Gateway Pattern**: Single entry point for clients
3. **Service Communication**: HTTP-based inter-service communication
4. **Shared Utilities**: Common code shared across services
5. **Environment Configuration**: Service-specific configurations
6. **Error Handling**: Centralized error handling
7. **Logging**: Structured logging across services
8. **Health Checks**: Service health monitoring

## Technologies Used

- **Node.js**: Runtime environment
- **Express.js**: Web framework
- **JWT**: Authentication tokens
- **Axios**: HTTP client for service communication
- **Morgan**: HTTP request logging
- **Helmet**: Security middleware
- **Cors**: Cross-origin resource sharing
- **Dotenv**: Environment variable management

## Next Steps

- Add database integration (MongoDB/PostgreSQL)
- Implement message queues (Redis/RabbitMQ)
- Add Docker containerization
- Implement service discovery (Consul/Eureka)
- Add monitoring and observability
- Implement circuit breaker pattern
- Add API documentation with Swagger
