{"name": "learn-microservices", "version": "1.0.0", "description": "A Node.js microservices learning project", "main": "index.js", "scripts": {"start": "node scripts/start-all.js", "start:user": "cd services/user-service && npm start", "start:product": "cd services/product-service && npm start", "start:gateway": "cd services/api-gateway && npm start", "install:all": "npm run install:user && npm run install:product && npm run install:gateway", "install:user": "cd services/user-service && npm install", "install:product": "cd services/product-service && npm install", "install:gateway": "cd services/api-gateway && npm install", "dev": "concurrently \"npm run start:user\" \"npm run start:product\" \"npm run start:gateway\"", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["microservices", "nodejs", "express", "api"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}