const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');
const { authenticateToken } = require('../../../shared/middleware/auth');
const { validateUser, validateLogin } = require('../middleware/validation');

// Public routes
router.post('/register', validateUser, userController.register);
router.post('/login', validateLogin, userController.login);

// Protected routes
router.get('/profile', authenticateToken, userController.getProfile);
router.put('/profile', authenticateToken, userController.updateProfile);
router.get('/me', authenticateToken, userController.getCurrentUser);

// Admin routes (for demonstration)
router.get('/', authenticateToken, userController.getAllUsers);
router.get('/:id', authenticateToken, userController.getUserById);
router.delete('/:id', authenticateToken, userController.deleteUser);

module.exports = router;
